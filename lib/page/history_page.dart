import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gsheets/gsheets.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/query/query_history_page.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/theme.dart';
import 'package:songanh/widgets/value_notifier_list.dart';
import 'package:substring_highlight/substring_highlight.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key, required this.wSheet});
  final Worksheet wSheet;
  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  final ValueNotifier<FilterType?> filterType = ValueNotifier(null);
  final ValueNotifier<double?> vSlider = ValueNotifier(1);
  final ValueNotifier<RangeValues?> vRange = ValueNotifier(RangeValues(1, 3));
  ValueNotifierList<Room> vRooms = ValueNotifierList([]);
  ValueNotifier<DateTime?> startDate = ValueNotifier(null);
  ValueNotifier<DateTime?> endDate = ValueNotifier(null);
  Timer? time;
  List<Room> roomTemplate = [];
  late TextEditingController controller;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RoomService.getRoomInfo(widget.wSheet).then((rooms) {
        roomTemplate = rooms;
        vRooms.setValue(rooms);
      });
    });
  }

  @override
  void dispose() {
    time?.cancel();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.surfaceColor,
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Modern Header Section
          Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppTheme.cardGradient,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.dividerColor,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide.none),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(
                                color: AppTheme.primaryBlue, width: 2)),
                        isDense: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        hintText: 'Tìm kiếm theo tên, số điện thoại...',
                        hintStyle: TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 16,
                        ),
                        prefixIcon: Container(
                          margin: EdgeInsets.all(12),
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.search_rounded,
                            color: AppTheme.primaryBlue,
                            size: 20,
                          ),
                        )),
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                    onChanged: (value) {
                      final curentRooms = roomTemplate
                          .where((final r) =>
                              (r.phone?.contains(value) ?? false) ||
                              (r.consumerName
                                      ?.toLowerCase()
                                      .contains(value.toLowerCase()) ??
                                  false))
                          .toList();
                      vRooms.setValue(curentRooms);
                    },
                  ),
                ),
                SizedBox(height: 20),
                // Filter Section Title
                Text(
                  'Bộ lọc tìm kiếm',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 16),
              ],
            ),
          ),

          // Filter Controls Section
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20),
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: ValueListenableBuilder(
                valueListenable: filterType,
                builder: (context, vType, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Filter Type Selection
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              gradient: AppTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: InkWell(
                                onTapDown: (details) {
                                  final offset = details.globalPosition;
                                  showMenu(
                                      context: context,
                                      position: RelativeRect.fromLTRB(
                                          50,
                                          offset.dy + 10,
                                          MediaQuery.sizeOf(context).width / 2,
                                          offset.dy),
                                      items: [
                                        PopupMenuItem(
                                          child: Row(
                                            children: [
                                              Icon(Icons.access_time_rounded,
                                                   color: AppTheme.primaryBlue, size: 20),
                                              SizedBox(width: 12),
                                              Text('Thời gian gần nhất',
                                                   style: TextStyle(fontWeight: FontWeight.w500)),
                                            ],
                                          ),
                                          onTap: () {
                                            filterType.value = FilterType.timeRecent;
                                          },
                                        ),
                                        PopupMenuItem(
                                          child: Row(
                                            children: [
                                              Icon(Icons.date_range_rounded,
                                                   color: AppTheme.primaryBlue, size: 20),
                                              SizedBox(width: 12),
                                              Text('Khoảng thời gian',
                                                   style: TextStyle(fontWeight: FontWeight.w500)),
                                            ],
                                          ),
                                          onTap: () {
                                            filterType.value = FilterType.rangeTime;
                                          },
                                        ),
                                      ]);
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.filter_list_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      vType == null
                                          ? 'Chọn kiểu lọc'
                                          : mapType[vType] ?? '',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Icon(
                                      Icons.keyboard_arrow_down_rounded,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ],
                                )),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      // Filter Options Display
                      if (vType == FilterType.timeRecent)
                        Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppTheme.primaryBlue.withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time_rounded,
                                color: AppTheme.primaryBlue,
                                size: 20,
                              ),
                              SizedBox(width: 12),
                              Expanded(
                                child: ValueListenableBuilder(
                                    valueListenable: vSlider,
                                    builder: (context, vMounth, child) {
                                      return Text(
                                          '${vMounth?.round().toString()} tháng gần nhất',
                                          style: TextStyle(
                                            color: AppTheme.primaryBlue,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 16,
                                          ),
                                          overflow: TextOverflow.ellipsis);
                                    }),
                              )
                            ],
                          ),
                        ),
                      if (vType == FilterType.rangeTime)
                        Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppTheme.primaryBlue.withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.date_range_rounded,
                                color: AppTheme.primaryBlue,
                                size: 20,
                              ),
                              SizedBox(width: 12),
                              Expanded(
                                child: ValueListenableBuilder(
                                    valueListenable: vRange,
                                    builder: (context, vRangeMounth, child) {
                                      return Text.rich(
                                          TextSpan(children: [
                                            TextSpan(
                                                text: 'Từ tháng ',
                                                style: TextStyle(
                                                  color: AppTheme.primaryBlue,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                children: [
                                                  TextSpan(
                                                      text: '${vRangeMounth?.start.round()}',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.w700,
                                                      ))
                                                ]),
                                            TextSpan(text: ' '),
                                            TextSpan(
                                                text: 'đến tháng ',
                                                style: TextStyle(
                                                  color: AppTheme.primaryBlue,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                children: [
                                                  TextSpan(
                                                      text: '${vRangeMounth?.end.round()}',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.w700,
                                                      ))
                                                ])
                                          ]),
                                          overflow: TextOverflow.ellipsis);
                                    }),
                              )
                            ],
                          ),
                        ),

                      // Slider Controls
                      if (vType == FilterType.timeRecent)
                        ValueListenableBuilder(
                            valueListenable: vSlider,
                            builder: (context, vMounth, child) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 16),
                                  Text(
                                    'Chọn số tháng',
                                    style: TextStyle(
                                      color: AppTheme.textPrimary,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      activeTrackColor: AppTheme.primaryBlue,
                                      inactiveTrackColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
                                      thumbColor: AppTheme.primaryBlue,
                                      overlayColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
                                      valueIndicatorColor: AppTheme.primaryBlue,
                                      valueIndicatorTextStyle: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    child: Slider(
                                        label: '${vMounth?.round().toString()} tháng',
                                        min: 1,
                                        max: 12,
                                        divisions: 11,
                                        value: vMounth ?? 1.0,
                                        onChanged: (val) {
                                          vSlider.value = val;
                                        },
                                        onChangeEnd: (final val) {
                                          RoomService.getRoomInfo(widget.wSheet)
                                              .then((rooms) {
                                            final listF = QueryHistoryPage
                                                .filterOrdersByMonths(
                                                    rooms, val.round());
                                            vRooms.setValue(listF);
                                          });
                                        }),
                                  ),
                                ],
                              );
                            }),
                      if (vType == FilterType.rangeTime)
                        ValueListenableBuilder(
                            valueListenable: vRange,
                            builder: (context, vRangeMounth, child) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 16),
                                  Text(
                                    'Chọn khoảng tháng',
                                    style: TextStyle(
                                      color: AppTheme.textPrimary,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      activeTrackColor: AppTheme.primaryBlue,
                                      inactiveTrackColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
                                      thumbColor: AppTheme.primaryBlue,
                                      overlayColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
                                      valueIndicatorColor: AppTheme.primaryBlue,
                                      valueIndicatorTextStyle: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    child: RangeSlider(
                                        min: 1,
                                        max: 12,
                                        divisions: 11,
                                        values: vRangeMounth ?? RangeValues(1, 3),
                                        labels: RangeLabels(
                                            'Tháng ${vRangeMounth?.start.round().toString() ?? '1'}',
                                            'Tháng ${vRangeMounth?.end.round().toString() ?? '3'}'),
                                        onChanged: (final val) {
                                          vRange.value = val;
                                        }),
                                  ),
                                ],
                              );
                            }),
                    ],
                  );
                }),
          ),

          // Results Section
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              child: ValueListenableBuilder(
                  valueListenable: vRooms,
                  builder: (context, vRoom, child) {
                    if (vRoom.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: AppTheme.surfaceColor,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.search_off_rounded,
                                size: 64,
                                color: AppTheme.textSecondary,
                              ),
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Không có dữ liệu',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }
                    return AnimationLimiter(
                      child: ListView.builder(
                        padding: EdgeInsets.only(top: 20, bottom: 100),
                        itemCount: vRoom.length,
                        itemBuilder: (context, index) {
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: Container(
                                  margin: EdgeInsets.only(bottom: 16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.05),
                                        blurRadius: 10,
                                        offset: Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: ValueListenableBuilder(
                                      valueListenable: controller,
                                      builder: (context, vText, child) {
                                        return _buildRoomCard(vRoom[index], vText.text, context);
                                      }),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomCard(Room room, String searchTerm, BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Room Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.hotel_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          room.roomId ?? '',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        SizedBox(width: 12),
                        if (room.type == 'VIP')
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppTheme.warningColor,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'VIP',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        Spacer(),
                        Text(
                          room.time ?? '',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppTheme.primaryBlue,
                          ),
                        ),
                      ],
                    ),
                    if (room.gia != null) ...[
                      SizedBox(height: 8),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: AppTheme.successGradient,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${NumberFormat('#,###', 'vi_VN').format(int.tryParse(room.gia ?? '') ?? 0)} VND',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 20),

          // Customer Info
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.person_rounded,
                        color: AppTheme.primaryBlue,
                        size: 16,
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: SubstringHighlight(
                        text: room.consumerName ?? '',
                        term: searchTerm,
                        textStyleHighlight: TextStyle(
                          color: AppTheme.primaryBlue,
                          backgroundColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
                          fontWeight: FontWeight.w600,
                        ),
                        textStyle: TextStyle(
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                if (room.phone?.isNotEmpty ?? false) ...[
                  SizedBox(height: 12),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.phone_rounded,
                          color: AppTheme.successColor,
                          size: 16,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: SubstringHighlight(
                          text: room.phone ?? '',
                          term: searchTerm,
                          textStyleHighlight: TextStyle(
                            color: AppTheme.successColor,
                            backgroundColor: AppTheme.successColor.withValues(alpha: 0.2),
                            fontWeight: FontWeight.w600,
                          ),
                          textStyle: TextStyle(
                            color: AppTheme.textPrimary,
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                SizedBox(height: 12),
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.dividerColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time_rounded,
                        color: AppTheme.textSecondary,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Nhận phòng: ',
                        style: TextStyle(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          _buildTine('', room.timeIn ?? ''),
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _buildTine(String title, String time) {
    try {
      DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
      return DateFormat('HH:mm - dd/MM/yyyy').format(dateTime);
    } catch (e) {
      return time;
    }
  }

  Map<FilterType, String> mapType = {
    FilterType.timeRecent: "Thời gian gần nhất",
    FilterType.rangeTime: "Khoảng thời gian",
  };
}

enum FilterType { timeRecent, rangeTime }